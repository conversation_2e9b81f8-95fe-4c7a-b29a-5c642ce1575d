'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

export default function QRCodePaymentPage() {
  const [paymentInfo, setPaymentInfo] = useState(null);
  const [confirmationCode, setConfirmationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: 扫码支付, 2: 确认支付
  
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  
  const planType = searchParams.get('plan');
  const amount = planType === 'monthly' ? 20 : 120;

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }
    
    // 生成支付信息
    const orderNo = `YP${Date.now()}${Math.random().toString(36).substring(2, 7)}`;
    setPaymentInfo({
      orderNo,
      amount,
      planType,
      userEmail: user.email
    });
  }, [user, planType, amount, router]);

  const handleConfirmPayment = async () => {
    if (!confirmationCode.trim()) {
      alert('请输入确认码');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/payment/manual-confirm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          orderNo: paymentInfo.orderNo,
          confirmationCode: confirmationCode.trim(),
          amount: paymentInfo.amount,
          planType: paymentInfo.planType
        })
      });

      const data = await response.json();

      if (response.ok) {
        alert('支付确认成功！会员已激活，请返回主页查看。');
        router.push('/dashboard');
      } else {
        alert('确认失败：' + data.error);
      }
    } catch (error) {
      alert('确认失败：' + error.message);
    } finally {
      setLoading(false);
    }
  };



  if (!paymentInfo) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* 头部 */}
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              {step === 1 ? '扫码支付' : '确认支付'}
            </h1>
            <p className="text-gray-600">
              {paymentInfo.planType === 'monthly' ? '月度会员' : '年度会员'} - ¥{paymentInfo.amount}
            </p>
          </div>

          {step === 1 && (
            <>
              {/* 订单信息 */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="text-sm space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">订单号:</span>
                    <span className="font-mono text-xs">{paymentInfo.orderNo}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">支付金额:</span>
                    <span className="font-bold text-red-600">¥{paymentInfo.amount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">用户邮箱:</span>
                    <span className="text-xs">{paymentInfo.userEmail}</span>
                  </div>
                </div>
              </div>

              {/* 支付方式选择 */}
              <div className="mb-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="border-2 border-blue-200 rounded-lg p-4">
                    <h3 className="font-semibold text-blue-800 mb-3 flex items-center justify-center">
                      <span className="text-xl mr-2">💰</span>
                      支付宝支付
                    </h3>
                    <div className="text-center">
                      <div className="bg-white p-4 rounded-xl inline-block shadow-lg">
                        <img
                          src="/pay/zfbpay.jpg"
                          alt="支付宝收款码"
                          className="w-48 h-48 rounded-lg object-cover"
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                          }}
                        />
                        <div className="w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center" style={{display: 'none'}}>
                          <span className="text-gray-500 text-sm text-center">
                            支付宝收款码<br/>
                            加载失败
                          </span>
                        </div>
                      </div>
                      <p className="text-xs text-blue-600 mt-2">长按保存或扫码支付</p>
                    </div>
                  </div>

                  <div className="border-2 border-green-200 rounded-lg p-4">
                    <h3 className="font-semibold text-green-800 mb-3 flex items-center justify-center">
                      <span className="text-xl mr-2">💚</span>
                      微信支付
                    </h3>
                    <div className="text-center">
                      <div className="bg-white p-4 rounded-xl inline-block shadow-lg">
                        <img
                          src="/pay/wxpay.jpg"
                          alt="微信收款码"
                          className="w-48 h-48 rounded-lg object-cover"
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                          }}
                        />
                        <div className="w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center" style={{display: 'none'}}>
                          <span className="text-gray-500 text-sm text-center">
                            微信收款码<br/>
                            加载失败
                          </span>
                        </div>
                      </div>
                      <p className="text-xs text-green-600 mt-2">长按保存或扫码支付</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 支付说明 */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h4 className="font-semibold text-yellow-800 mb-2">📋 支付流程：</h4>
                <ol className="text-sm text-yellow-700 space-y-2">
                  <li className="flex items-start">
                    <span className="bg-yellow-200 text-yellow-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">1</span>
                    <span>使用支付宝或微信扫描上方二维码</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-yellow-200 text-yellow-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">2</span>
                    <span>支付金额：<strong className="text-red-600">¥{paymentInfo.amount}</strong></span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-yellow-200 text-yellow-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">3</span>
                    <span>支付备注填写：<code className="bg-yellow-200 px-1 rounded">{paymentInfo.orderNo}</code></span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-yellow-200 text-yellow-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">4</span>
                    <span>支付完成后点击"已完成支付"按钮</span>
                  </li>
                  <li className="flex items-start">
                    <span className="bg-yellow-200 text-yellow-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">5</span>
                    <span>联系客服获取确认码并输入激活会员</span>
                  </li>
                </ol>
              </div>

              {/* 操作按钮 */}
              <div className="space-y-3">
                <button
                  onClick={() => setStep(2)}
                  className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-6 rounded-lg font-medium hover:from-green-700 hover:to-green-800 transition-all duration-200"
                >
                  ✅ 已完成支付
                </button>
                
                <button
                  onClick={() => router.push('/subscription')}
                  className="w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                >
                  取消支付
                </button>
              </div>
            </>
          )}

          {step === 2 && (
            <>
              {/* 确认支付 */}
              <div className="mb-6">
                <p className="text-gray-600 mb-4">
                  请联系客服确认您的支付，并获取确认码：
                </p>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <h4 className="font-semibold text-blue-800 mb-2">联系客服获取确认码：</h4>
                  <div className="text-sm text-blue-700 space-y-1">
                    <p>📧 邮箱：<EMAIL></p>
                    <p>💬 微信：请扫描上方微信收款码添加</p>
                    <p>⏰ 服务时间：9:00-21:00</p>
                    <p className="text-xs text-blue-600 mt-2">
                      💡 提示：支付完成后请提供订单号和支付截图
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      确认码
                    </label>
                    <input
                      type="text"
                      value={confirmationCode}
                      onChange={(e) => setConfirmationCode(e.target.value)}
                      placeholder="请输入客服提供的确认码"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="space-y-3">
                <button
                  onClick={handleConfirmPayment}
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50"
                >
                  {loading ? '确认中...' : '确认支付'}
                </button>

                <button
                  onClick={() => setStep(1)}
                  className="w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                >
                  返回上一步
                </button>
              </div>
            </>
          )}

          {/* 客服信息 */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <p className="text-xs text-gray-600 mb-2">
                🕐 客服服务时间：9:00-21:00
              </p>
              <p className="text-xs text-gray-600">
                📧 如有问题，请联系：<EMAIL>
              </p>
              <p className="text-xs text-gray-500 mt-1">
                我们会在1小时内回复您的消息
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
