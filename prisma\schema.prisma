// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// 用户表
model User {
  id                Int      @id @default(autoincrement())
  email             String   @unique
  passwordHash      String   @map("password_hash")
  username          String?
  role              String   @default("user") // 'user' 或 'super_admin'
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  isActive          Boolean  @default(true) @map("is_active")
  emailVerified     Boolean  @default(false) @map("email_verified")
  verificationToken String?  @map("verification_token")

  // 关联关系
  subscriptions Subscription[]
  payments      Payment[]

  @@map("users")
}

// 订阅表
model Subscription {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  planType  String   @map("plan_type") // 'monthly' 或 'yearly'
  status    String   @default("active") // 'active', 'expired', 'cancelled'
  startDate DateTime @map("start_date")
  endDate   DateTime @map("end_date")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  user     User      @relation(fields: [userId], references: [id])
  payments Payment[]

  @@map("subscriptions")
}

// 支付记录表
model Payment {
  id             Int       @id @default(autoincrement())
  userId         Int       @map("user_id")
  subscriptionId Int?      @map("subscription_id")
  amount         Float
  currency       String    @default("CNY")
  paymentMethod  String?   @map("payment_method") // 'alipay', 'wechat'
  paymentStatus  String    @default("pending") @map("payment_status") // 'pending', 'completed', 'failed', 'refunded'
  transactionId  String?   @map("transaction_id") // 第三方支付平台的交易ID
  createdAt      DateTime  @default(now()) @map("created_at")
  completedAt    DateTime? @map("completed_at")

  // 关联关系
  user         User          @relation(fields: [userId], references: [id])
  subscription Subscription? @relation(fields: [subscriptionId], references: [id])

  @@map("payments")
}
