# 🚀 简化生产环境部署指南

## 📋 最小化部署清单

### 🔧 服务器要求
- **最低配置**: 1GB RAM, 1 CPU, 10GB 存储
- **推荐配置**: 2GB RAM, 1 CPU, 20GB 存储
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- **必需软件**: Docker

### 🚀 一键部署步骤

#### 1. 服务器准备
```bash
# 安装 Docker (Ubuntu/Debian)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker
```

#### 2. 部署应用
```bash
# 上传项目文件到服务器
# 或者直接在服务器上克隆
git clone <your-repo-url>
cd youpin-sentinel

# 一键部署
chmod +x quick-deploy.sh
./quick-deploy.sh
```

#### 3. 访问应用
- 访问: `http://你的服务器IP:3000`
- 管理员账号: `<EMAIL>`
- 管理员密码: `Admin123456`

### 🔒 基础安全配置

#### 修改管理员密码
1. 登录系统
2. 进入个人资料页面
3. 立即修改密码

#### 配置防火墙
```bash
# Ubuntu/Debian
sudo ufw allow 22    # SSH
sudo ufw allow 3000  # 应用端口
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

### 📊 日常维护

#### 查看应用状态
```bash
docker ps                    # 查看容器状态
docker logs youpin-app       # 查看应用日志
```

#### 备份数据
```bash
docker exec youpin-app npm run backup
```

#### 重启应用
```bash
docker restart youpin-app
```

#### 停止应用
```bash
docker stop youpin-app
```

### 🌐 域名配置（可选）

如果你有域名，可以配置反向代理：

#### 安装 Nginx
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 配置 Nginx
```bash
sudo nano /etc/nginx/sites-available/youpin
```

添加配置：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/youpin /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 🔄 自动备份设置

添加定时备份：
```bash
crontab -e
```

添加以下行（每天凌晨2点备份）：
```
0 2 * * * docker exec youpin-app npm run backup
```

### 🚨 故障排除

#### 应用无法启动
```bash
# 查看详细日志
docker logs youpin-app

# 重新构建
docker stop youpin-app
docker rm youpin-app
./quick-deploy.sh
```

#### 端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep :3000

# 修改端口（编辑 quick-deploy.sh 中的端口号）
```

#### 数据库问题
```bash
# 重置数据库
docker exec youpin-app npx prisma db push --force-reset
docker exec youpin-app node scripts/create-super-admin.js
```

### 📈 性能优化

#### 启用 Gzip（如使用 Nginx）
在 Nginx 配置中添加：
```nginx
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
```

#### 设置缓存
```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    proxy_pass http://localhost:3000;
}
```

---

## ✅ 部署完成检查

- [ ] 应用正常启动
- [ ] 可以正常访问登录页面
- [ ] 管理员账号可以登录
- [ ] 价格数据正常显示
- [ ] 已修改默认密码
- [ ] 已配置防火墙
- [ ] 已设置自动备份

## 🎯 总结

这个简化方案提供：
- ✅ 零配置数据库（SQLite）
- ✅ 一键部署脚本
- ✅ 自动备份功能
- ✅ 基础安全配置
- ✅ 简单维护命令

适合中小型项目快速上线使用！
