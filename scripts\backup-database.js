const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 数据库备份脚本
async function backupDatabase() {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = process.env.BACKUP_PATH || './backups';
    
    // 确保备份目录存在
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // SQLite 备份
    const dbPath = './prisma/dev.db';
    if (fs.existsSync(dbPath)) {
      const backupPath = path.join(backupDir, `database-${timestamp}.db`);
      fs.copyFileSync(dbPath, backupPath);
      console.log(`✅ SQLite 数据库备份成功: ${backupPath}`);
      
      // 压缩备份文件
      try {
        execSync(`gzip "${backupPath}"`);
        console.log(`✅ 备份文件已压缩: ${backupPath}.gz`);
      } catch (error) {
        console.log('⚠️ 压缩失败，但备份文件已保存');
      }
    }

    // 清理旧备份（保留最近7天）
    const files = fs.readdirSync(backupDir);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    files.forEach(file => {
      const filePath = path.join(backupDir, file);
      const stats = fs.statSync(filePath);
      if (stats.mtime < sevenDaysAgo && file.startsWith('database-')) {
        fs.unlinkSync(filePath);
        console.log(`🗑️ 删除旧备份: ${file}`);
      }
    });

    console.log('🎉 数据库备份完成!');
  } catch (error) {
    console.error('❌ 数据库备份失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  backupDatabase();
}

module.exports = { backupDatabase };
