const nextConfig = {
  images: {
    domains: ['youpin.img898.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'youpin.img898.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  // 生产环境安全配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          }
        ],
      },
    ];
  },

  // 生产环境优化
  compress: true,
  poweredByHeader: false,

  // 输出配置
  output: 'standalone',
};

module.exports = nextConfig;
