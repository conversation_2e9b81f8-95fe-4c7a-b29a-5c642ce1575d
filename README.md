# 优品商品价格监控系统

一个基于 Next.js 的商品价格监控和会员管理系统。

## 功能特性

- 🔍 **商品价格监控**: 实时监控目标商品价格变化
- 💰 **溢价分析**: 智能分析商品溢价情况
- 👥 **会员系统**: 完整的用户注册、登录和订阅管理
- 💳 **支付管理**: 支持手动确认支付和会员激活
- 📊 **数据展示**: 直观的价格趋势和统计信息
- 🔔 **通知系统**: 企业微信机器人推送价格提醒
- 📱 **响应式设计**: 适配桌面端和移动端
- 🎨 **现代化界面**: 基于 Tailwind CSS 的美观设计

## 技术栈

- **前端**: Next.js 15, React, Tailwind CSS
- **后端**: Next.js API Routes
- **数据库**: SQLite + Prisma ORM
- **认证**: JWT Token
- **支付**: 手动确认支付系统

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

创建 `.env` 文件：

```env
DATABASE_URL="file:./dev.db"
JWT_SECRET="your-jwt-secret-key"
NEXT_PUBLIC_BASE_URL="http://localhost:3001"
```

### 3. 数据库初始化

```bash
npx prisma generate
npx prisma db push
```

### 4. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3001](http://localhost:3001) 查看应用。

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # 管理员页面
│   │   ├── users/         # 用户管理
│   │   └── payment-confirm/ # 支付确认码生成
│   ├── api/               # API 路由
│   │   ├── auth/          # 认证相关 API
│   │   ├── admin/         # 管理员 API
│   │   ├── payment/       # 支付相关 API
│   │   ├── templates/     # 商品模板 API
│   │   └── user/          # 用户相关 API
│   ├── dashboard/         # 仪表板页面
│   ├── login/            # 登录页面
│   ├── register/         # 注册页面
│   ├── payment/          # 支付相关页面
│   ├── profile/          # 个人资料页面
│   └── subscription/     # 订阅管理页面
├── contexts/              # React Context
│   └── AuthContext.js    # 认证上下文
├── data/                  # 数据配置
│   ├── constants.js      # 常量定义
│   ├── items.js          # 商品数据
│   ├── premium-config.js # 溢价配置
│   └── templates.js      # 模板数据
├── lib/                   # 工具库
│   ├── auth.js           # 认证工具
│   ├── payment.js        # 支付工具
│   └── prisma.js         # 数据库连接
└── ...

## 主要功能

### 用户管理
- 用户注册和登录
- 个人资料管理
- 会员状态查看

### 商品监控
- 实时价格数据获取
- 溢价商品筛选
- 价格趋势分析

### 支付系统
- 订阅计划管理
- 手动支付确认
- 会员权限控制

### 管理功能
- 超级管理员系统
- 用户管理（角色分配、状态管理）
- 支付确认码生成器
- 权限控制

## 部署

### 生产环境构建

```bash
npm run build
npm start
```

### 环境变量

确保在生产环境中设置以下环境变量：
- `DATABASE_URL`
- `JWT_SECRET`
- `NEXT_PUBLIC_BASE_URL`

## 开发指南

### 添加新功能
1. 在 `src/app/api/` 中添加 API 路由
2. 在 `src/app/` 中添加页面组件
3. 更新数据库模式（如需要）

### 数据库更改
```bash
npx prisma db push
npx prisma generate
```

## 许可证

MIT License
#   y o u p i n - s e n t i n e l 
 
 