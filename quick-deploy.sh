#!/bin/bash

# 快速部署脚本 - 适用于测试和小型部署
set -e

echo "🚀 快速部署优品商品价格监控系统..."

# 检查 Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 生成随机密钥
generate_secret() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# 创建生产环境配置
echo "🔧 生成生产环境配置..."
cat > .env << EOF
# 生产环境配置 - 自动生成
NODE_ENV=production
DATABASE_URL="file:./prisma/production.db"
JWT_SECRET="$(generate_secret)"
NEXTAUTH_SECRET="$(generate_secret)"
NEXTAUTH_URL="http://localhost:3000"
NEXT_PUBLIC_BASE_URL="http://localhost:3000"
EOF

echo "✅ 环境配置已生成"

# 创建必要目录
mkdir -p data logs backups

# 构建并启动
echo "🔨 构建应用..."
docker build -t youpin-app .

echo "🚀 启动应用..."
docker run -d \
  --name youpin-app \
  --restart unless-stopped \
  -p 3000:3000 \
  -v $(pwd)/data:/app/prisma \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/backups:/app/backups \
  --env-file .env \
  youpin-app

# 等待启动
echo "⏳ 等待应用启动..."
sleep 15

# 初始化数据库
echo "🗄️ 初始化数据库..."
docker exec youpin-app npx prisma db push

# 创建管理员
echo "👤 创建管理员账号..."
docker exec youpin-app node scripts/create-super-admin.js

# 健康检查
echo "🔍 健康检查..."
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo ""
    echo "🎉 部署成功！"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "🌐 访问地址: http://localhost:3000"
    echo "👤 管理员邮箱: <EMAIL>"
    echo "🔑 管理员密码: Admin123456"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo "⚠️  重要提醒："
    echo "1. 请立即登录并修改管理员密码"
    echo "2. 如需域名访问，请配置反向代理"
    echo "3. 生产环境建议配置 HTTPS"
    echo ""
    echo "🔧 管理命令："
    echo "  查看日志: docker logs youpin-app"
    echo "  停止应用: docker stop youpin-app"
    echo "  重启应用: docker restart youpin-app"
    echo "  备份数据: docker exec youpin-app npm run backup"
else
    echo "❌ 部署失败，查看日志："
    docker logs youpin-app
    exit 1
fi
