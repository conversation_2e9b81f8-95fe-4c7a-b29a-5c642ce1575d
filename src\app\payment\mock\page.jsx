'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';

export default function MockPaymentPage() {
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [paymentCompleted, setPaymentCompleted] = useState(false);
  
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const orderNo = searchParams.get('orderNo');
  const amount = searchParams.get('amount');
  const method = searchParams.get('method');

  useEffect(() => {
    if (paymentCompleted && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (paymentCompleted && countdown === 0) {
      window.close();
    }
  }, [paymentCompleted, countdown]);

  const handlePayment = async (success = true) => {
    setLoading(true);
    
    try {
      // 模拟支付处理时间
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 调用支付回调API
      const response = await fetch('/api/payment/callback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderNo,
          status: success ? 'success' : 'failed',
          transactionId: `TXN_${Date.now()}`
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setPaymentCompleted(true);
        setCountdown(5);
      } else {
        alert('支付处理失败: ' + result.message);
      }
    } catch (error) {
      alert('支付处理出错: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (paymentCompleted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4 text-center">
          <div className="text-6xl mb-4">🎉</div>
          <h1 className="text-2xl font-bold text-green-600 mb-2">支付成功！</h1>
          <p className="text-gray-600 mb-4">
            您的订阅已激活，感谢您的支持！
          </p>
          <p className="text-sm text-gray-500 mb-6">
            页面将在 {countdown} 秒后自动关闭
          </p>
          <button
            onClick={() => window.close()}
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            关闭页面
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
      <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4">
        <div className="text-center mb-8">
          <div className="text-4xl mb-4">
            {method === 'alipay' ? '💰' : '💚'}
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            {method === 'alipay' ? '支付宝支付' : '微信支付'}
          </h1>
          <p className="text-gray-600">
            这是一个模拟支付页面，用于演示支付流程
          </p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">订单号:</span>
            <span className="font-mono text-sm">{orderNo}</span>
          </div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">支付金额:</span>
            <span className="font-bold text-lg text-red-600">¥{amount}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">支付方式:</span>
            <span className="text-gray-800">
              {method === 'alipay' ? '支付宝' : '微信支付'}
            </span>
          </div>
        </div>

        <div className="space-y-3">
          <button
            onClick={() => handlePayment(true)}
            disabled={loading}
            className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-6 rounded-lg font-medium hover:from-green-700 hover:to-green-800 transition-all duration-200 disabled:opacity-50"
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                处理中...
              </span>
            ) : (
              '✅ 模拟支付成功'
            )}
          </button>
          
          <button
            onClick={() => handlePayment(false)}
            disabled={loading}
            className="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-3 px-6 rounded-lg font-medium hover:from-red-700 hover:to-red-800 transition-all duration-200 disabled:opacity-50"
          >
            ❌ 模拟支付失败
          </button>
          
          <button
            onClick={() => window.close()}
            disabled={loading}
            className="w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            取消支付
          </button>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>注意:</strong> 这是一个演示页面。在实际项目中，您需要集成真实的支付平台API（如支付宝、微信支付等）。
          </p>
        </div>
      </div>
    </div>
  );
}
