'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function SuperAdminUsersPage() {
  const { user, token, loading: authLoading } = useAuth();
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editingUser, setEditingUser] = useState(null);
  const [updateLoading, setUpdateLoading] = useState(false);

  // 检查超级管理员权限
  useEffect(() => {
    if (!authLoading) {
      if (!user || user.role !== 'super_admin') {
        router.push('/dashboard');
        return;
      }
      fetchUsers();
    }
  }, [authLoading, user, router]);

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/admin/users', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '获取用户列表失败');
      }

      setUsers(data.users);
    } catch (error) {
      console.error('获取用户列表错误:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // 更新用户信息
  const updateUser = async (userId, updates) => {
    try {
      setUpdateLoading(true);
      const response = await fetch('/api/admin/users', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, ...updates }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '更新用户失败');
      }

      // 更新本地状态
      setUsers(users.map(u => u.id === userId ? { ...u, ...updates } : u));
      setEditingUser(null);

    } catch (error) {
      setError(error.message);
    } finally {
      setUpdateLoading(false);
    }
  };

  // 切换用户状态
  const toggleUserStatus = async (userId, currentStatus) => {
    await updateUser(userId, { isActive: !currentStatus });
  };

  // 更改用户角色
  const changeUserRole = async (userId, newRole) => {
    await updateUser(userId, { role: newRole });
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* 头部 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            用户管理
          </h1>
          <p className="text-gray-600 mb-4">管理系统中的所有用户账号</p>

          {error && (
            <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
              ❌ {error}
            </div>
          )}

          <div className="flex justify-center space-x-4">
            <Link href="/dashboard">
              <button className="text-blue-600 hover:text-blue-500 transition-colors">
                ← 返回仪表板
              </button>
            </Link>
            <Link href="/admin">
              <button className="text-purple-600 hover:text-purple-500 transition-colors">
                系统管理 →
              </button>
            </Link>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总用户数</p>
                <p className="text-2xl font-bold text-blue-600">{users.length}</p>
              </div>
              <div className="text-3xl">👥</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">活跃会员</p>
                <p className="text-2xl font-bold text-green-600">
                  {users.filter(user => 
                    user.subscriptions.some(sub => 
                      sub.status === 'active' && new Date(sub.endDate) > new Date()
                    )
                  ).length}
                </p>
              </div>
              <div className="text-3xl">💎</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">今日注册</p>
                <p className="text-2xl font-bold text-purple-600">
                  {users.filter(user => {
                    const today = new Date();
                    const userDate = new Date(user.createdAt);
                    return userDate.toDateString() === today.toDateString();
                  }).length}
                </p>
              </div>
              <div className="text-3xl">🆕</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总收入</p>
                <p className="text-2xl font-bold text-orange-600">
                  ¥{users.reduce((total, user) => 
                    total + user.payments
                      .filter(p => p.paymentStatus === 'completed')
                      .reduce((sum, p) => sum + p.amount, 0), 0
                  )}
                </p>
              </div>
              <div className="text-3xl">💰</div>
            </div>
          </div>
        </div>

        {/* 用户列表 */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-800">注册用户列表</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">用户信息</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">角色</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">状态</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">会员状态</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">注册时间</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-700">操作</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => {
                  const activeSubscription = user.subscriptions.find(sub => 
                    sub.status === 'active' && new Date(sub.endDate) > new Date()
                  );
                  const completedPayments = user.payments.filter(p => p.paymentStatus === 'completed');
                  
                  return (
                    <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-6">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {user.username || '未设置'}
                          </div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                          <div className="text-xs text-gray-400">ID: {user.id}</div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <select
                          value={user.role || 'user'}
                          onChange={(e) => changeUserRole(user.id, e.target.value)}
                          disabled={updateLoading}
                          className="text-sm border border-gray-300 rounded px-2 py-1"
                        >
                          <option value="user">普通用户</option>
                          <option value="super_admin">超级管理员</option>
                        </select>
                      </td>
                      <td className="py-4 px-6">
                        <button
                          onClick={() => toggleUserStatus(user.id, user.isActive)}
                          disabled={updateLoading}
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            user.isActive
                              ? 'bg-green-100 text-green-800 hover:bg-green-200'
                              : 'bg-red-100 text-red-800 hover:bg-red-200'
                          }`}
                        >
                          {user.isActive ? '✅ 活跃' : '❌ 禁用'}
                        </button>
                      </td>
                      <td className="py-4 px-6">
                        {activeSubscription ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            💎 {activeSubscription.planType === 'monthly' ? '月度' : '年度'}会员
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            ⭕ 未订阅
                          </span>
                        )}
                      </td>
                      <td className="py-4 px-6 text-sm text-gray-500">
                        {new Date(user.createdAt).toLocaleDateString()}
                      </td>
                      <td className="py-4 px-6 text-sm">
                        <div className="text-center">
                          <div className="font-semibold">{completedPayments.length}次</div>
                          {completedPayments.length > 0 && (
                            <div className="text-xs text-gray-500">
                              ¥{completedPayments.reduce((sum, p) => sum + p.amount, 0)}
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {users.length === 0 && (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">👤</div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">暂无用户</h3>
              <p className="text-gray-600">还没有用户注册</p>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="mt-8 text-center">
          <button
            onClick={fetchUsers}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            🔄 刷新数据
          </button>
        </div>
      </div>
    </div>
  );
}
