'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

export default function ProfilePage() {
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [username, setUsername] = useState('');
  
  const { user, isAuthenticated, token, fetchUserProfile } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    // 每次进入个人中心都刷新用户数据，确保订阅状态是最新的
    loadUserProfile();
  }, [isAuthenticated, router]);

  const loadUserProfile = async () => {
    try {
      const profile = await fetchUserProfile();
      if (profile) {
        setUserProfile(profile);
        setUsername(profile.username || '');
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    setUpdating(true);

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ username })
      });

      const data = await response.json();

      if (response.ok) {
        setUserProfile({ ...userProfile, username: data.user.username });
        alert('用户信息更新成功！');
      } else {
        alert('更新失败: ' + data.error);
      }
    } catch (error) {
      alert('更新失败: ' + error.message);
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">加载用户信息失败</h1>
          <Link href="/">
            <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              返回首页
            </button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* 头部 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            个人中心
          </h1>
          <Link href="/dashboard">
            <button className="text-blue-600 hover:text-blue-500 transition-colors">
              ← 返回仪表板
            </button>
          </Link>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* 用户信息 */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">用户信息</h2>
            
            <form onSubmit={handleUpdateProfile} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱地址
                </label>
                <input
                  type="email"
                  value={userProfile.email}
                  disabled
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  用户名
                </label>
                <input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入用户名"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  注册时间
                </label>
                <input
                  type="text"
                  value={new Date(userProfile.createdAt).toLocaleString()}
                  disabled
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                />
              </div>

              <button
                type="submit"
                disabled={updating}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50"
              >
                {updating ? '更新中...' : '更新信息'}
              </button>
            </form>
          </div>

          {/* 订阅状态 */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-800">订阅状态</h2>
              <button
                onClick={loadUserProfile}
                className="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors"
              >
                🔄 刷新
              </button>
            </div>
            
{(() => {
              const activeSubscription = userProfile.subscriptions && userProfile.subscriptions.length > 0
                ? userProfile.subscriptions.find(sub => sub.status === 'active' && new Date(sub.endDate) > new Date())
                : null;

              return activeSubscription ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">订阅状态:</span>
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                      ✅ 活跃
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">订阅类型:</span>
                    <span className="font-medium">
                      {activeSubscription.planType === 'monthly' ? '月度会员' : '年度会员'}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">开始时间:</span>
                    <span>{new Date(activeSubscription.startDate).toLocaleDateString()}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">到期时间:</span>
                    <span>{new Date(activeSubscription.endDate).toLocaleDateString()}</span>
                  </div>

                  {(() => {
                    const daysRemaining = Math.ceil((new Date(activeSubscription.endDate) - new Date()) / (1000 * 60 * 60 * 24));
                    return daysRemaining > 0 ? (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">剩余天数:</span>
                        <span className="font-bold text-blue-600">
                          {daysRemaining} 天
                        </span>
                      </div>
                    ) : null;
                  })()}

                  <Link href="/subscription">
                    <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-pink-700 transition-all duration-200 mt-4">
                      续费订阅
                    </button>
                  </Link>
                </div>
              ) : (
              <div className="text-center py-8">
                <div className="text-4xl mb-4">📦</div>
                <p className="text-gray-600 mb-4">您还没有订阅</p>
                <Link href="/subscription">
                  <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200">
                    立即订阅
                  </button>
                </Link>
              </div>
              );
            })()}
          </div>
        </div>

        {/* 支付历史 */}
        {userProfile.paymentHistory && userProfile.paymentHistory.length > 0 && (
          <div className="bg-white rounded-xl shadow-lg p-6 mt-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">支付历史</h2>
            
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4">支付时间</th>
                    <th className="text-left py-3 px-4">金额</th>
                    <th className="text-left py-3 px-4">支付方式</th>
                    <th className="text-left py-3 px-4">状态</th>
                  </tr>
                </thead>
                <tbody>
                  {userProfile.paymentHistory.map((payment) => (
                    <tr key={payment.id} className="border-b border-gray-100">
                      <td className="py-3 px-4">
                        {new Date(payment.createdAt).toLocaleString()}
                      </td>
                      <td className="py-3 px-4 font-medium">
                        ¥{payment.amount}
                      </td>
                      <td className="py-3 px-4">
                        {payment.paymentMethod === 'alipay' ? '支付宝' : '微信支付'}
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          payment.paymentStatus === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : payment.paymentStatus === 'failed'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {payment.paymentStatus === 'completed' ? '已完成' :
                           payment.paymentStatus === 'failed' ? '失败' : '处理中'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
