version: '3.8'
services:
  mysql:
    image: mysql:5.7.40
    container_name: youpin-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: youpin123456
      MYSQL_DATABASE: youpin_sentinel
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

volumes:
  mysql_data:
