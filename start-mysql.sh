#!/bin/bash

echo "🚀 启动 MySQL 数据库..."
echo ""
echo "选择启动方式:"
echo "1. 使用 Docker (推荐)"
echo "2. 使用本地 MySQL 服务"
echo ""

read -p "请输入选择 (1 或 2): " choice

if [ "$choice" = "1" ]; then
    echo ""
    echo "🐳 使用 Docker 启动 MySQL..."
    docker-compose -f docker-mysql.yml up -d
    
    if [ $? -eq 0 ]; then
        echo "✅ MySQL 容器启动成功!"
        echo "📋 连接信息:"
        echo "   主机: localhost"
        echo "   端口: 3306"
        echo "   用户: root"
        echo "   密码: youpin123456"
        echo "   数据库: youpin_sentinel"
        echo ""
        echo "⏳ 等待 MySQL 启动完成..."
        sleep 10
    else
        echo "❌ Docker 启动失败，请检查 Docker 是否已安装并运行"
        exit 1
    fi
elif [ "$choice" = "2" ]; then
    echo ""
    echo "🔧 启动本地 MySQL 服务..."
    
    # 尝试不同的启动命令
    if command -v systemctl &> /dev/null; then
        sudo systemctl start mysql
    elif command -v service &> /dev/null; then
        sudo service mysql start
    elif command -v brew &> /dev/null; then
        brew services start mysql
    else
        echo "❌ 无法找到 MySQL 服务管理命令"
        exit 1
    fi
    
    if [ $? -eq 0 ]; then
        echo "✅ MySQL 服务启动成功!"
    else
        echo "❌ MySQL 服务启动失败，请检查 MySQL 是否已安装"
        exit 1
    fi
else
    echo "❌ 无效选择"
    exit 1
fi

echo ""
echo "🔄 初始化数据库结构..."
npx prisma db push

if [ $? -eq 0 ]; then
    echo "✅ 数据库结构创建成功!"
    echo ""
    echo "👤 创建超级管理员账号..."
    node scripts/create-super-admin.js
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "🎉 数据库设置完成!"
        echo "📋 超级管理员账号:"
        echo "   邮箱: <EMAIL>"
        echo "   密码: Admin123456"
        echo ""
        echo "🚀 启动开发服务器..."
        npm run dev
    else
        echo "❌ 创建超级管理员失败"
    fi
else
    echo "❌ 数据库初始化失败，请检查连接配置"
fi
