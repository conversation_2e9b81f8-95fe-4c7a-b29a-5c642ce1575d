# 🚀 生产环境部署检查清单

## 📋 部署前准备

### 🔒 安全配置
- [ ] 修改 `.env.production` 中的所有密钥
  - [ ] `JWT_SECRET` - 至少32位强密码
  - [ ] `NEXTAUTH_SECRET` - 强密码
  - [ ] 数据库密码（如使用MySQL）
- [ ] 配置 HTTPS 证书
- [ ] 设置防火墙规则
- [ ] 配置域名和DNS

### 🗄️ 数据库配置
- [ ] 选择数据库类型（SQLite推荐用于中小型项目）
- [ ] 配置数据库连接字符串
- [ ] 设置数据库备份策略
- [ ] 测试数据库连接

### 🌐 服务器配置
- [ ] 服务器规格确认（推荐最低2GB内存）
- [ ] 安装 Docker 和 Docker Compose
- [ ] 配置 Nginx 反向代理
- [ ] 设置日志轮转

## 🔧 部署步骤

### 1. 环境准备
```bash
# 克隆项目
git clone <your-repo-url>
cd youpin-sentinel

# 复制并配置环境变量
cp .env.production .env
# 编辑 .env 文件，修改所有密钥和配置
```

### 2. SSL 证书配置
```bash
# 将SSL证书放入ssl目录
mkdir ssl
# 复制证书文件
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem
```

### 3. 部署应用
```bash
# 使用部署脚本
chmod +x deploy.sh
./deploy.sh

# 或手动部署
npm run docker:build
npm run docker:run
```

### 4. 验证部署
- [ ] 访问健康检查端点: `https://yourdomain.com/api/health`
- [ ] 测试登录功能
- [ ] 验证数据库连接
- [ ] 检查日志输出

## 📊 监控和维护

### 监控设置
- [ ] 设置定时健康检查
```bash
# 添加到 crontab
*/5 * * * * cd /path/to/youpin-sentinel && npm run monitor
```

- [ ] 配置日志监控
- [ ] 设置告警通知

### 备份策略
- [ ] 设置自动数据库备份
```bash
# 每日备份 (添加到 crontab)
0 2 * * * cd /path/to/youpin-sentinel && npm run backup
```

- [ ] 测试备份恢复流程
- [ ] 配置异地备份

### 性能优化
- [ ] 启用 Gzip 压缩
- [ ] 配置静态资源缓存
- [ ] 设置 CDN（可选）
- [ ] 数据库索引优化

## 🔐 安全加固

### 应用安全
- [ ] 启用速率限制
- [ ] 配置 CORS 策略
- [ ] 设置安全头
- [ ] 定期更新依赖

### 服务器安全
- [ ] 禁用不必要的端口
- [ ] 配置 fail2ban
- [ ] 设置自动安全更新
- [ ] 定期安全扫描

## 📈 性能监控

### 关键指标
- [ ] 响应时间监控
- [ ] 内存使用率监控
- [ ] 磁盘空间监控
- [ ] 数据库性能监控

### 告警设置
- [ ] 应用宕机告警
- [ ] 高内存使用告警
- [ ] 磁盘空间不足告警
- [ ] 数据库连接失败告警

## 🚨 应急预案

### 故障处理
- [ ] 准备回滚方案
- [ ] 建立故障联系人
- [ ] 制定故障处理流程
- [ ] 定期演练应急预案

### 数据恢复
- [ ] 数据库恢复流程
- [ ] 配置文件备份
- [ ] 应用代码备份
- [ ] 测试恢复流程

## 📞 上线后检查

### 功能验证
- [ ] 用户注册/登录
- [ ] 价格数据获取
- [ ] 支付功能（如启用）
- [ ] 管理员功能
- [ ] 邮件通知（如启用）

### 性能验证
- [ ] 页面加载速度
- [ ] API 响应时间
- [ ] 并发用户测试
- [ ] 长时间运行稳定性

## 📝 文档更新
- [ ] 更新部署文档
- [ ] 记录配置变更
- [ ] 更新运维手册
- [ ] 培训运维人员

---

## 🎯 推荐配置

### 小型部署（< 1000用户）
- 服务器: 2GB RAM, 1 CPU, 20GB SSD
- 数据库: SQLite
- 备份: 每日本地备份

### 中型部署（1000-10000用户）
- 服务器: 4GB RAM, 2 CPU, 50GB SSD
- 数据库: MySQL 5.7
- 备份: 每日本地+异地备份
- 监控: 基础监控告警

### 大型部署（> 10000用户）
- 服务器: 8GB+ RAM, 4+ CPU, 100GB+ SSD
- 数据库: MySQL 集群或云数据库
- 备份: 实时备份+多地备份
- 监控: 全面监控+APM
- 负载均衡: 多实例部署
