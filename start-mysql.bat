@echo off
echo 🚀 启动 MySQL 数据库...

echo.
echo 选择启动方式:
echo 1. 使用 Docker (推荐)
echo 2. 使用本地 MySQL 服务
echo.

set /p choice="请输入选择 (1 或 2): "

if "%choice%"=="1" (
    echo.
    echo 🐳 使用 Docker 启动 MySQL...
    docker-compose -f docker-mysql.yml up -d
    if %errorlevel% equ 0 (
        echo ✅ MySQL 容器启动成功!
        echo 📋 连接信息:
        echo    主机: localhost
        echo    端口: 3306
        echo    用户: root
        echo    密码: youpin123456
        echo    数据库: youpin_sentinel
        echo.
        echo ⏳ 等待 MySQL 启动完成...
        timeout /t 10 /nobreak > nul
    ) else (
        echo ❌ Docker 启动失败，请检查 Docker 是否已安装并运行
        pause
        exit /b 1
    )
) else if "%choice%"=="2" (
    echo.
    echo 🔧 启动本地 MySQL 服务...
    net start mysql
    if %errorlevel% equ 0 (
        echo ✅ MySQL 服务启动成功!
    ) else (
        echo ❌ MySQL 服务启动失败，请检查 MySQL 是否已安装
        pause
        exit /b 1
    )
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo 🔄 初始化数据库结构...
npx prisma db push

if %errorlevel% equ 0 (
    echo ✅ 数据库结构创建成功!
    echo.
    echo 👤 创建超级管理员账号...
    node scripts/create-super-admin.js
    
    if %errorlevel% equ 0 (
        echo.
        echo 🎉 数据库设置完成!
        echo 📋 超级管理员账号:
        echo    邮箱: <EMAIL>
        echo    密码: Admin123456
        echo.
        echo 🚀 启动开发服务器...
        npm run dev
    ) else (
        echo ❌ 创建超级管理员失败
    )
) else (
    echo ❌ 数据库初始化失败，请检查连接配置
)

pause
