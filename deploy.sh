#!/bin/bash

# 生产环境部署脚本
set -e

echo "🚀 开始部署优品商品价格监控系统..."

# 检查必要的文件
if [ ! -f ".env.production" ]; then
    echo "❌ 缺少 .env.production 文件，请先配置生产环境变量"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data
mkdir -p backups
mkdir -p logs
mkdir -p ssl

# 备份现有数据库（如果存在）
if [ -f "data/production.db" ]; then
    echo "💾 备份现有数据库..."
    cp data/production.db backups/production-backup-$(date +%Y%m%d-%H%M%S).db
fi

# 构建 Docker 镜像
echo "🔨 构建 Docker 镜像..."
docker build -t youpin-app .

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose -f docker-compose.prod.yml down || true

# 启动新容器
echo "🚀 启动新容器..."
docker-compose -f docker-compose.prod.yml up -d

# 等待应用启动
echo "⏳ 等待应用启动..."
sleep 10

# 初始化数据库
echo "🗄️ 初始化数据库..."
docker-compose -f docker-compose.prod.yml exec app npx prisma db push

# 创建超级管理员（如果不存在）
echo "👤 创建超级管理员..."
docker-compose -f docker-compose.prod.yml exec app node scripts/create-super-admin.js

# 健康检查
echo "🔍 健康检查..."
sleep 5
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ 应用启动成功！"
    echo "🌐 访问地址: http://localhost:3000"
    echo "👤 管理员账号: <EMAIL>"
    echo "🔑 管理员密码: Admin123456"
    echo ""
    echo "⚠️  重要提醒："
    echo "1. 请立即修改管理员密码"
    echo "2. 配置 SSL 证书"
    echo "3. 设置定时备份"
    echo "4. 配置监控告警"
else
    echo "❌ 应用启动失败，请检查日志："
    docker-compose -f docker-compose.prod.yml logs app
    exit 1
fi

echo "🎉 部署完成！"
