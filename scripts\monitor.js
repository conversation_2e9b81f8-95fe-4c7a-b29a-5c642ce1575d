const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 系统监控脚本
class SystemMonitor {
  constructor() {
    this.logFile = path.join(__dirname, '../logs/monitor.log');
    this.alertThresholds = {
      memoryUsage: 80, // 内存使用率超过80%告警
      diskUsage: 85,   // 磁盘使用率超过85%告警
      responseTime: 5000, // 响应时间超过5秒告警
    };
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    
    console.log(logMessage.trim());
    
    // 确保日志目录存在
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    fs.appendFileSync(this.logFile, logMessage);
  }

  async checkHealth() {
    try {
      const start = Date.now();
      const response = await fetch('http://localhost:3000/api/health');
      const responseTime = Date.now() - start;
      
      if (response.ok) {
        const data = await response.json();
        this.log(`✅ 健康检查通过 - 响应时间: ${responseTime}ms`);
        
        if (responseTime > this.alertThresholds.responseTime) {
          this.log(`⚠️ 响应时间过长: ${responseTime}ms`);
        }
        
        return { status: 'healthy', responseTime, data };
      } else {
        this.log(`❌ 健康检查失败 - HTTP ${response.status}`);
        return { status: 'unhealthy', responseTime };
      }
    } catch (error) {
      this.log(`❌ 健康检查异常: ${error.message}`);
      return { status: 'error', error: error.message };
    }
  }

  checkSystemResources() {
    try {
      // 检查内存使用率
      const memInfo = execSync('free -m').toString();
      const memLines = memInfo.split('\n');
      const memData = memLines[1].split(/\s+/);
      const totalMem = parseInt(memData[1]);
      const usedMem = parseInt(memData[2]);
      const memUsage = Math.round((usedMem / totalMem) * 100);

      // 检查磁盘使用率
      const diskInfo = execSync('df -h /').toString();
      const diskLines = diskInfo.split('\n');
      const diskData = diskLines[1].split(/\s+/);
      const diskUsage = parseInt(diskData[4].replace('%', ''));

      this.log(`📊 系统资源 - 内存: ${memUsage}%, 磁盘: ${diskUsage}%`);

      // 检查告警阈值
      if (memUsage > this.alertThresholds.memoryUsage) {
        this.log(`⚠️ 内存使用率过高: ${memUsage}%`);
      }

      if (diskUsage > this.alertThresholds.diskUsage) {
        this.log(`⚠️ 磁盘使用率过高: ${diskUsage}%`);
      }

      return { memUsage, diskUsage };
    } catch (error) {
      this.log(`❌ 系统资源检查失败: ${error.message}`);
      return null;
    }
  }

  async checkDatabase() {
    try {
      const dbPath = path.join(__dirname, '../prisma/production.db');
      if (fs.existsSync(dbPath)) {
        const stats = fs.statSync(dbPath);
        const sizeInMB = Math.round(stats.size / 1024 / 1024 * 100) / 100;
        this.log(`💾 数据库大小: ${sizeInMB}MB`);
        return { size: sizeInMB, lastModified: stats.mtime };
      } else {
        this.log(`❌ 数据库文件不存在: ${dbPath}`);
        return null;
      }
    } catch (error) {
      this.log(`❌ 数据库检查失败: ${error.message}`);
      return null;
    }
  }

  async runFullCheck() {
    this.log('🔍 开始系统监控检查...');
    
    const results = {
      timestamp: new Date().toISOString(),
      health: await this.checkHealth(),
      resources: this.checkSystemResources(),
      database: await this.checkDatabase()
    };

    // 生成监控报告
    const reportPath = path.join(__dirname, '../logs/monitor-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));

    this.log('✅ 监控检查完成');
    return results;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const monitor = new SystemMonitor();
  monitor.runFullCheck().catch(console.error);
}

module.exports = SystemMonitor;
