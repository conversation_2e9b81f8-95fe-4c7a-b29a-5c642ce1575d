{"name": "youpin-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "backup": "node scripts/backup-database.js", "monitor": "node scripts/monitor.js", "setup:admin": "node scripts/create-super-admin.js", "prod:build": "NODE_ENV=production npm run build", "prod:start": "NODE_ENV=production npm start", "docker:build": "docker build -t youpin-app .", "docker:run": "docker-compose -f docker-compose.prod.yml up -d", "docker:stop": "docker-compose -f docker-compose.prod.yml down", "deploy": "chmod +x deploy.sh && ./deploy.sh"}, "dependencies": {"@prisma/client": "^6.12.0", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "15.4.1", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4"}}