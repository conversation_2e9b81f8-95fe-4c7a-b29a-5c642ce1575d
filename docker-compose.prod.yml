version: '3.8'

services:
  app:
    build: .
    container_name: youpin-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:./prisma/production.db
      - JWT_SECRET=${JWT_SECRET}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL}
    volumes:
      - ./data:/app/prisma
      - ./backups:/app/backups
      - ./logs:/app/logs
    networks:
      - youpin-network
    depends_on:
      - nginx
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    container_name: youpin-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - youpin-network
    depends_on:
      - app

  # 可选：添加 MySQL 数据库
  # mysql:
  #   image: mysql:5.7
  #   container_name: youpin-mysql
  #   restart: unless-stopped
  #   environment:
  #     MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
  #     MYSQL_DATABASE: youpin_sentinel
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   networks:
  #     - youpin-network

networks:
  youpin-network:
    driver: bridge

volumes:
  mysql_data:
